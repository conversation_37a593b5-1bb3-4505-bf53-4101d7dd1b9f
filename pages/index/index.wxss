/* index.wxss */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Header Styles */
.header {
  padding: 60rpx 40rpx 40rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 56rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
}

.stats {
  display: flex;
  justify-content: center;
  gap: 60rpx;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 24rpx 32rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 8rpx;
}

/* Todo Section */
.todo-section {
  flex: 1;
  padding: 40rpx;
  padding-bottom: 200rpx;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  margin: 40rpx 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 36rpx;
  color: #666;
  font-weight: 600;
  display: block;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #999;
  display: block;
}

/* Todo List */
.todo-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

.todo-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item:active {
  background: rgba(102, 126, 234, 0.05);
  transform: scale(0.98);
}

.todo-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.checkbox-wrapper {
  position: relative;
  margin-right: 24rpx;
}

.checkbox {
  opacity: 0;
  position: absolute;
  z-index: 2;
  width: 44rpx;
  height: 44rpx;
}

.custom-checkbox {
  width: 44rpx;
  height: 44rpx;
  border: 3rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: white;
}

.custom-checkbox.checked {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
  transform: scale(1.1);
}

.checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.custom-checkbox.checked .checkmark {
  opacity: 1;
}

.todo-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.completed .todo-text {
  text-decoration: line-through;
  color: #999;
  opacity: 0.6;
}

.todo-actions {
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.todo-item:hover .todo-actions {
  opacity: 1;
}

.action-icon {
  font-size: 32rpx;
  color: #999;
  padding: 16rpx;
}

/* Input Section */
.input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 40rpx;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-container {
  display: flex;
  align-items: center;
  gap: 24rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

.input-wrapper {
  flex: 1;
  position: relative;
  background: white;
  border-radius: 60rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.2);
  transform: translateY(-2rpx);
}

.input-icon {
  position: absolute;
  left: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  z-index: 1;
}

.input {
  width: 100%;
  padding: 32rpx 32rpx 32rpx 80rpx;
  border: none;
  border-radius: 60rpx;
  font-size: 32rpx;
  color: #333;
  background: transparent;
  outline: none;
}

.input::placeholder {
  color: #999;
}

.add-button {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.add-button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

.add-button[disabled] {
  background: #ddd;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transform: none;
}

.button-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
  line-height: 1;
}

.add-button[disabled] .button-icon {
  color: #999;
}

/* Animations */
@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.todo-item {
  animation: fadeIn 0.3s ease;
}

.input-section {
  animation: slideInUp 0.4s ease;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .header {
    padding: 40rpx 30rpx 30rpx;
  }

  .title {
    font-size: 48rpx;
  }

  .stats {
    gap: 40rpx;
  }

  .stat-item {
    padding: 20rpx 24rpx;
  }

  .todo-section {
    padding: 30rpx;
  }

  .input-section {
    padding: 30rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .todo-list {
    background: rgba(40, 40, 40, 0.95);
  }

  .todo-item {
    background: #2a2a2a;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .todo-text {
    color: #e0e0e0;
  }

  .input-wrapper {
    background: #2a2a2a;
    border-color: rgba(102, 126, 234, 0.3);
  }

  .input {
    color: #e0e0e0;
  }

  .input::placeholder {
    color: #888;
  }

  .input-section {
    background: rgba(40, 40, 40, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .empty-state {
    background: rgba(40, 40, 40, 0.95);
  }

  .empty-text {
    color: #e0e0e0;
  }

  .empty-subtitle {
    color: #888;
  }
}
