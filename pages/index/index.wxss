/* index.wxss */
.container {
  padding: 20px;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* Completed todo item styling */
.completed text {
  text-decoration: line-through;
}

.title {
  font-size: 24px;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

.todo-list {
  list-style-type: none;
  padding-left: 0;
}

.todo-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

.todo-item:last-child {
  border-bottom: none;
}

.checkbox {
  margin-right: 10px;
}

.input-box {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.input {
  flex: 1;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-right: 10px;
}

.button {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.button:hover {
  background-color: #0056b3;
}
