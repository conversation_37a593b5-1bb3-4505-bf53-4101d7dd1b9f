// index.js

Page({
  data: {
    todos: [],
    newTodoText: "",
    completedCount: 0
  },
  onLoad() {
    const app = getApp();
    this.setData({
      todos: app.globalData.todos,
      completedCount: this.getCompletedCount(app.globalData.todos)
    });
  },

  getCompletedCount(todos) {
    return todos.filter(todo => todo.completed).length;
  },

  updateCompletedCount() {
    const completedCount = this.getCompletedCount(this.data.todos);
    this.setData({ completedCount });
  },
  onInputChange(e) {
    this.setData({ newTodoText: e.detail.value });
  },
  addTodo() {
    const { todos, newTodoText } = this.data;
    if (newTodoText.trim()) {
      const newTodo = {
        id: Date.now().toString(),
        text: newTodoText.trim(),
        completed: false
      };
      const app = getApp();
      app.globalData.todos.push(newTodo);
      this.setData({
        todos: app.globalData.todos,
        newTodoText: ""
      });
      this.updateCompletedCount();

      // 添加触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
    }
  },
  toggleComplete(e) {
    e.stopPropagation(); // 防止事件冒泡
    const { id } = e.currentTarget.dataset;
    const { todos } = this.data;
    const todoIndex = todos.findIndex(todo => todo.id === id);
    if (todoIndex !== -1) {
      todos[todoIndex].completed = !todos[todoIndex].completed;
      const app = getApp();
      app.globalData.todos = todos;
      this.setData({ todos });
      this.updateCompletedCount();

      // 添加触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
    }
  },
  handleItemTap(e) {
    // Get the item index from the tap event
    const { index } = e.currentTarget.dataset;
    if (index !== undefined) {
      const { todos } = this.data;
      todos[index].completed = !todos[index].completed;

      // Update global data and local state
      const app = getApp();
      app.globalData.todos = todos;
      this.setData({ todos });
      this.updateCompletedCount();

      // 添加触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
    }
  }
})
