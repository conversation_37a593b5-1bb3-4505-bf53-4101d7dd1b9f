// index.js

Page({
  data: {
    todos: [],
    newTodoText: ""
  },
  onLoad() {
    const app = getApp();
    this.setData({ todos: app.globalData.todos });
  },
  onInputChange(e) {
    this.setData({ newTodoText: e.detail.value });
  },
  addTodo() {
    const { todos, newTodoText } = this.data;
    if (newTodoText.trim()) {
      const newTodo = {
        id: Date.now().toString(),
        text: newTodoText,
        completed: false
      };
      const app = getApp();
      app.globalData.todos.push(newTodo);
      this.setData({
        todos: app.globalData.todos,
        newTodoText: ""
      });
    }
  },
  toggleComplete(e) {
    const { id } = e.currentTarget.dataset;
    const { todos } = this.data;
    const todoIndex = todos.findIndex(todo => todo.id === id);
    if (todoIndex !== -1) {
      todos[todoIndex].completed = !todos[todoIndex].completed;
      const app = getApp();
      app.globalData.todos = todos;
      this.setData({ todos });
    }
  },
  handleItemTap(e) {
    const { id } = e.currentTarget.dataset;
    if (id) {
      // If the click was on a checkbox, we don't need to do anything here
      // as toggleComplete is already handled
      return;
    }

    // Get the item index from the tap event
    const { index } = e.currentTarget.dataset;
    if (index !== undefined) {
      const { todos } = this.data;
      todos[index].completed = !todos[index].completed;

      // Update global data and local state
      const app = getApp();
      app.globalData.todos = todos;
      this.setData({ todos });
    }
  }
})
