<!-- index.wxml -->
<view class="container">
  <!-- Header Section -->
  <view class="header">
    <view class="header-content">
      <text class="title">✨ My Todo List</text>
      <text class="subtitle">Stay organized and productive</text>
    </view>
    <view class="stats">
      <view class="stat-item">
        <text class="stat-number">{{todos.length}}</text>
        <text class="stat-label">Total</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{completedCount}}</text>
        <text class="stat-label">Done</text>
      </view>
    </view>
  </view>

  <!-- Todo List Section -->
  <view class="todo-section">
    <view wx:if="{{todos.length === 0}}" class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-text">No todos yet</text>
      <text class="empty-subtitle">Add your first task below!</text>
    </view>

    <view wx:else class="todo-list">
      <view wx:for="{{todos}}" wx:key="id" class="todo-item {{item.completed ? 'completed' : ''}}" bindtap="handleItemTap" data-index="{{index}}">
        <view class="todo-content">
          <view class="checkbox-wrapper">
            <checkbox class="checkbox" checked="{{item.completed}}" data-id="{{item.id}}" bindtap="toggleComplete"></checkbox>
            <view class="custom-checkbox {{item.completed ? 'checked' : ''}}">
              <text class="checkmark">✓</text>
            </view>
          </view>
          <text class="todo-text">{{item.text}}</text>
        </view>
        <view class="todo-actions">
          <text class="action-icon">⋯</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Input Section -->
  <view class="input-section">
    <view class="input-container">
      <view class="input-wrapper">
        <text class="input-icon">✏️</text>
        <input
          type="text"
          placeholder="What needs to be done?"
          class="input"
          bindinput="onInputChange"
          value="{{newTodoText}}"
          confirm-type="done"
          bindconfirm="addTodo"
        />
      </view>
      <button class="add-button" bindtap="addTodo" disabled="{{!newTodoText.trim()}}">
        <text class="button-icon">+</text>
      </button>
    </view>
  </view>
</view>
