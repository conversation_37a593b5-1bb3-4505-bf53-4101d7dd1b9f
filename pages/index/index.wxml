<!-- index.wxml -->
<view class="container">
  <text class="title">Todo List</text>
  <view wx:for="{{todos}}" wx:key="id" class="todo-item {{item.completed ? 'completed' : ''}}" bindtap="handleItemTap" data-index="{{index}}">
    <checkbox class="checkbox" checked="{{item.completed}}" data-id="{{item.id}}" bindtap="toggleComplete"></checkbox>
    <text>{{item.text}}</text>
  </view>
  <view class="input-box">
    <input type="text" placeholder="Add new todo" class="input" bindinput="onInputChange" />
    <button class="button" bindtap="addTodo">Add</button>
  </view>
</view>
