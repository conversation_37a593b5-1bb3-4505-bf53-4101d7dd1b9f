/* app.wxss */

/* 全局样式重置 */
page {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 移除通配符选择器，改为具体的元素选择器 */
view, text, button, input {
  box-sizing: border-box;
}

/* 全局动画 */
.fade-in {
  animation: fadeIn 0.3s ease;
}

.slide-up {
  animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 全局按钮样式 */
button {
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;
}

button::after {
  border: none;
}

/* 全局输入框样式 */
input {
  outline: none;
}

/* 移除webkit滚动条样式，小程序不支持 */
